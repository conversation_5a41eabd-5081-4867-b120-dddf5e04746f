import React from 'react';
import { Button, Input, Typography, Form, Select, Card } from 'antd';

const { Title, Paragraph, Text } = Typography;

/**
 * 字体测试页面 - 用于验证字体统一效果
 */
const FontTest: React.FC = () => {
  return (
    <div className="p-8 max-w-4xl mx-auto">
      <Title level={1} className="text-center mb-8">
        字体统一测试页面
      </Title>
      
      <Card title="HTML 原生元素" className="mb-6">
        <div className="space-y-4">
          <h2>这是 h2 标题</h2>
          <p>这是普通段落文字，应该使用统一的字体栈。</p>
          <div className="font-sans">使用 font-sans 类的文字</div>
          <div className="font-inter">使用 font-inter 类的文字（现在应该和默认字体一致）</div>
          <div className="font-arial">使用 font-arial 类的文字（现在应该和默认字体一致）</div>
          <code className="font-mono">这是等宽字体代码文字</code>
        </div>
      </Card>

      <Card title="Ant Design 组件" className="mb-6">
        <div className="space-y-4">
          <Typography>
            <Title level={2}>Ant Design Typography 标题</Title>
            <Paragraph>
              这是 Ant Design Typography 组件的段落文字，应该与页面其他文字使用相同字体。
            </Paragraph>
            <Text strong>这是加粗文字</Text>
            <br />
            <Text italic>这是斜体文字</Text>
            <br />
            <Text code>这是行内代码文字</Text>
          </Typography>
          
          <Form layout="vertical">
            <Form.Item label="输入框标签">
              <Input placeholder="这是输入框占位符文字" />
            </Form.Item>
            <Form.Item label="选择框标签">
              <Select placeholder="这是选择框占位符文字">
                <Select.Option value="option1">选项一</Select.Option>
                <Select.Option value="option2">选项二</Select.Option>
              </Select>
            </Form.Item>
          </Form>
          
          <div className="space-x-4">
            <Button type="primary">主要按钮</Button>
            <Button>默认按钮</Button>
            <Button type="link">链接按钮</Button>
          </div>
        </div>
      </Card>

      <Card title="UnoCSS 工具类测试">
        <div className="space-y-2">
          <div className="text-sm">小号文字 (text-sm)</div>
          <div className="text-base">基础文字 (text-base)</div>
          <div className="text-lg">大号文字 (text-lg)</div>
          <div className="text-xl">特大文字 (text-xl)</div>
          <div className="font-bold">粗体文字 (font-bold)</div>
          <div className="font-light">细体文字 (font-light)</div>
        </div>
      </Card>
    </div>
  );
};

export default FontTest;
