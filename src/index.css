/* UnoCSS 样式将在 main.tsx 中导入 */

/* 自定义颜色变量和字体变量 */
:root {
  /* 页面背景色 */
  --color-page-bg: #0d0d0d;
  /* 主色调 */
  --color-primary: #ff5e13;
  /*主按钮文字颜色*/
  --color-primary-text: #000000;
  /* 主按钮激活颜色 */
  --color-primary-active: #e5541a;
  /* 主按钮禁用颜色 */
  --color-primary-disabled: rgba(255, 94, 19, 0.45);
  /*主按钮禁用文字颜色*/
  --color-primary-disabled-text: rgba(0, 0, 0, 0.45);

  /* form表单label颜色 */
  --color-label: #656565;
  /* form表单项颜色 */
  --color-form-item: #c9c9c9;
  /* 按钮文字颜色 */
  --color-button-text: #000000;

  /* 字体变量 - 简化字体栈，优先Arial */
  --font-family: Aria<PERSON>, 'Segoe UI', sans-serif;
  --font-family-mono:
    'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;
}

/* 全局最小宽度设置和字体设置 */
html,
body {
  min-width: 1200px;
  background-color: var(--color-page-bg);
  font-family: var(--font-family);
}

#root {
  min-width: 1200px; /* 确保根容器也有最小宽度 */
}

/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--color-page-bg);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: var(--color-label);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #888888;
}
